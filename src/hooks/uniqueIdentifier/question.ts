import {
  getChildQuestionsAccessor,
  isSection
} from "@helpers/configurationFormHelper";

import { Section } from "@src/types/FormConfiguration";
import {
  MultiLevelQuestion,
  Question,
  QuestionTypes
} from "@src/types/Question";

export const createUniqueIdentifier = ({
  text = "",
  existingIdentifiers = [],
  camelCase = false
}: {
  text: string;
  existingIdentifiers?: string[];
  camelCase?: boolean;
}) => {
  const baseIdentifier = text
    .replace(/[^a-zA-Z0-9 ]/g, "")
    .split(" ")
    .reduce(
      (acc, word, index) =>
        acc +
        (index === 0 && camelCase
          ? word.charAt(0)
          : word.charAt(0).toUpperCase()) +
        word.slice(1),
      ""
    );
  let identifier = baseIdentifier;
  let counter = 1;
  while (
    existingIdentifiers?.some(
      existingIdentifier =>
        existingIdentifier?.toLowerCase() === identifier?.toLowerCase()
    )
  ) {
    identifier = `${baseIdentifier}_${counter}`;
    counter++;
  }
  return identifier;
};

// Specific for questions
export const autogenerateIdentifierHelper = ({
  question,
  text,
  parentQuestion,
  withinContent,
  camelCase
}: {
  question: Question;
  text: string;
  parentQuestion?: Question;
  withinContent: Section[] | Question[];
  camelCase?: boolean;
}) => {
  const identifierPrefix = createUniqueIdentifier({
    text,
    camelCase: camelCase || parentQuestion?.type === QuestionTypes.JSON
  });
  let identifier = identifierPrefix;

  let version = 1;
  while (
    isDuplicateIdentifier({
      question,
      identifier,
      parentQuestion,
      withinContent
    })
  ) {
    identifier = `${identifierPrefix}_${version}`;
    version += 1;
  }
  return identifier;
};

export const isDuplicateIdentifier = ({
  question,
  identifier,
  parentQuestion,
  withinContent
}: {
  question: Question;
  identifier?: string;
  parentQuestion?: Question;
  withinContent: Section[] | Question[];
}) => {
  const { formQuestions, childToParentMap } =
    findFormQuestionsAndChildToParentMap(withinContent);
  console.log(
    "findFormQuestionsAndChildToParentMap",
    formQuestions,
    childToParentMap
  );
  const parent = parentQuestion || childToParentMap.get(question.id);

  const siblingQuestions = parent
    ? (
        (parent as MultiLevelQuestion).properties?.[
          getChildQuestionsAccessor(parent.type, question.type)
        ] || []
      ).filter(q => q.id !== question.id)
    : formQuestions.filter(q => q.id !== question.id);

  return siblingQuestions.some(
    q =>
      q.identifier?.toUpperCase() ===
      (identifier?.toUpperCase() ?? question.identifier?.toUpperCase())
  );
};

const findFormQuestionsAndChildToParentMap = (
  withinContent: Section[] | Question[]
): {
  formQuestions: Question[];
  childToParentMap: Map<string, Question>;
} => {
  const formQuestions: Question[] = [];
  const childToParentMap = new Map<string, Question>();

  const traverseItem = (item: Section | Question) => {
    if (isSection(item)) {
      item.content.forEach(traverseItem);
    } else {
      formQuestions.push(item);
      visitChild(item, (parent, child) => {
        childToParentMap.set(child.id, parent);
      });
    }
  };

  withinContent.forEach(traverseItem);

  return { formQuestions, childToParentMap };
};

export const visitChild = (
  question: Question,
  handleChild: (parent: Question, child: Question) => void
) => {
  (question as MultiLevelQuestion).properties?.[
    getChildQuestionsAccessor(question.type)
  ]?.forEach(child => {
    handleChild(question, child);
    visitChild(child, handleChild);
  });

  if (question.type === QuestionTypes.TABLE) {
    (question as MultiLevelQuestion).properties?.[
      getChildQuestionsAccessor(question.type, QuestionTypes.CHART)
    ]?.forEach(child => {
      handleChild(question, child);
      visitChild(child, handleChild);
    });
  }
};
