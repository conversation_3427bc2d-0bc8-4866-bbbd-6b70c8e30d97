import React, { useState } from "react";

import {
  ChartType,
  DropdownItem,
  DropdownItemGroup,
  Icon,
  WhiteboardAddLine
} from "@oneteam/onetheme";

import { getQuestionTypeOptionsForForms } from "@helpers/configurationFormHelper";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";

import { useDictionary } from "@src/hooks/useDictionary";
import { ChartQuestionTypes, QuestionTypes } from "@src/types/Question.ts";

export const ConfigurationFormAddLine = ({
  addQuestion,
  addSection,
  questionTypes = getQuestionTypeOptionsForForms(),
  questionText,
  addChart,
  additionalDropdownItems
}: {
  addQuestion?: (questionType: QuestionTypes) => void;
  addSection?: () => void;
  questionTypes?: QuestionTypes[];
  questionText?: string;
  addChart?: (chartType: ChartType) => void;
  additionalDropdownItems?: ({
    handleClose,
    isOpen
  }: {
    handleClose: () => void;
    isOpen: boolean;
  }) => React.ReactNode;
}) => {
  const d = useDictionary();
  const [isOpen, setIsOpen] = useState(false);

  const [isHighlighted, setIsHighlighted] = useState(false);

  return (
    <WhiteboardAddLine
      isOpen={isOpen}
      isHighlighted={isHighlighted}
      onMouseEnter={() => setIsHighlighted(true)}
      onMouseLeave={() => setIsHighlighted(false)}
      onOpenChange={setIsOpen}
      dropdownMenuContent={
        <>
          {addQuestion && (
            <DropdownItemGroup
              title={questionText ?? d("ui.configuration.forms.question.title")}
            >
              {questionTypes.map((type: QuestionTypes) => (
                <DropdownItem
                  id={type}
                  key={`question-${type}`}
                  onClick={() => {
                    addQuestion(type);
                    setIsHighlighted(false);
                    setIsOpen(false);
                  }}
                  description={d(
                    `ui.configuration.forms.question.type.${type}.description`
                  )}
                >
                  <QuestionType type={type} size="regular" />
                </DropdownItem>
              ))}
            </DropdownItemGroup>
          )}
          {addSection && (
            <DropdownItemGroup hasDivider={!!addQuestion}>
              <DropdownItem
                id="view"
                onClick={() => {
                  addSection();
                  setIsHighlighted(false);
                  setIsOpen(false);
                }}
                leftElement={<Icon name="add" />}
              >
                {d("ui.configuration.forms.section.add")}
              </DropdownItem>
            </DropdownItemGroup>
          )}
          {addChart && (
            <DropdownItemGroup
              title={
                questionText ??
                d("ui.configuration.forms.question.type.chart.type.title")
              }
            >
              {ChartQuestionTypes.map(type => (
                <DropdownItem
                  id={type}
                  key={`chart-${type}`}
                  onClick={() => {
                    addChart(type);
                    setIsHighlighted(false);
                    setIsOpen(false);
                  }}
                  description={d(
                    `ui.configuration.forms.question.type.chart.type.${type}.description`
                  )}
                >
                  <QuestionType type={type} size="regular" />
                </DropdownItem>
              ))}
            </DropdownItemGroup>
          )}
          {additionalDropdownItems?.({
            handleClose: () => setIsOpen(false),
            isOpen
          })}
        </>
      }
    />
  );
};
