import React, { useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import {
  Box,
  ColorText,
  FontWeight,
  Heading,
  HeadingSize,
  Icon,
  IconButton,
  Inline,
  Label,
  Renamable,
  Stack,
  WhiteboardBlock,
  getClassNames
} from "@oneteam/onetheme";

import { DocumentErrorHelper } from "@helpers/DocumentErrorHelper.ts";
import { isMultiLevelQuestion } from "@helpers/configurationFormHelper.ts";
import { eventWithoutPropagationDefault } from "@helpers/eventWithoutPropagation.ts";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";
import { ReorderIcon } from "@components/shared/ReorderIcon.tsx";
import { WhiteboardStatusCircle } from "@components/shared/WhiteboardStatusCircle.tsx";

import { commonIcons } from "@src/constants/iconConstants.ts";
import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { FormPath } from "@src/types/Form.ts";
import { ConfigurationFormMode } from "@src/types/FormConfiguration.ts";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  ChartQuestionProperties,
  TableQuestionProperties
} from "@src/types/QuestionProperties.ts";

import { ChartProperties } from "../ConfigurationQuestionModal/QuestionConfigurationFields/Chart/ChartProperties.tsx";
import { MultiLevelProperties } from "../ConfigurationQuestionModal/QuestionConfigurationFields/MultiLevel/MultiLevelProperties.tsx";
import "./ConfigurationQuestionBlock.scss";

export const ConfigurationQuestionBlock = ({
  question,
  onClick,
  path,
  selectedQuestionPath,
  isHighlighted,
  isSelected,
  isDisabled,
  mode,
  handleEdit,
  handleDuplicate,
  handleDelete,
  handleRename,
  showReorder,
  className = "",
  style = {},
  onlyContainOneQuestion,
  hideMultiLevel = false,
  isRenaming = false
}: {
  question: Question;
  onClick?: (path: FormPath) => void;
  path: FormPath;
  selectedQuestionPath?: string;
  isHighlighted?: boolean;
  isSelected?: boolean;
  isDisabled?: boolean;
  mode: `${ConfigurationFormMode}`;
  handleEdit?: () => void;
  handleDuplicate?: () => void;
  handleDelete?: () => void;
  handleRename?: (name: string) => void;
  showReorder?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onlyContainOneQuestion?: boolean;
  hideMultiLevel?: boolean;
  isRenaming?: boolean;
}) => {
  const d = useDictionary();

  const { docErrorHelper } = useOutletContext<{
    docErrorHelper: DocumentErrorHelper;
  }>();

  const isMultiLevel = useMemo(
    () => isMultiLevelQuestion(question.type),
    [question]
  );

  const [isExpanded, setIsExpanded] = useState(true);

  const [isChartsExpanded, setIsChartsExpanded] = useState(true);

  const isQuestionBlockSelected = useMemo(() => {
    if (isSelected) {
      return true;
    }
    const currentPath = path.join(".");
    if (!isExpanded) {
      return selectedQuestionPath?.includes(currentPath);
    }
    return selectedQuestionPath === currentPath;
  }, [isSelected, path, isExpanded, selectedQuestionPath]);

  const hasChartsInTableQuestion = useMemo(() => {
    if (question?.type === QuestionTypes.TABLE) {
      return (
        ((question?.properties as TableQuestionProperties)?.charts?.length ??
          0) > 0
      );
    }
    return false;
  }, [question]);

  if (!question) {
    return <></>;
  }

  return (
    <WhiteboardBlock
      key={question.id}
      onClick={() => onClick?.(path)}
      isSelected={isQuestionBlockSelected}
      isHighlighted={isHighlighted}
      className={getClassNames(["question-configuration-block", className])}
      style={style}
      isDisabled={isDisabled}
    >
      {docErrorHelper.getErrorCountByPrefix(`$.${path.join(".")}`, true) >
        0 && (
        <WhiteboardStatusCircle
          tooltip={d("errors.configurationForm.question.tooltip")}
        />
      )}
      <Inline
        className="question-configuration-block__header"
        width="100"
        style={{
          padding: "var(--spacing-100) var(--spacing-150)"
        }}
        alignment="left"
        spaceBetween
      >
        <Stack gap="025">
          <QuestionType
            type={
              question?.type === QuestionTypes.CHART
                ? (question?.properties as ChartQuestionProperties).chartConfig
                    .type
                : question?.type
            }
            color={ColorText.TERTIARY}
          />
          {!onlyContainOneQuestion && (
            <Inline gap="050" alignment="left">
              {question.properties?.hidden && (
                <Icon name="visibility_off" size="s" color="text-secondary" />
              )}
              {question.properties?.disabled && (
                <Icon name="edit_off" size="s" color="text-secondary" />
              )}
              <Heading size={HeadingSize.XXS} weight={FontWeight.MEDIUM}>
                {handleRename && mode === "edit" ? (
                  <Renamable
                    value={question?.text}
                    onChange={handleRename}
                    controlFocus={isRenaming}
                  />
                ) : (
                  question.text
                )}
              </Heading>
              <Label
                className="question-configuration-block__label"
                label={" "}
                required={question?.properties?.required}
              />
            </Inline>
          )}
        </Stack>
        {mode === ConfigurationFormMode.EDIT && !onlyContainOneQuestion && (
          <Inline
            className="question-configuration-block__actions"
            height="fit"
            gap="300"
          >
            <Inline gap="150">
              {handleEdit && mode === "edit" && (
                <IconButton
                  {...commonIcons.update}
                  skipFocus
                  label={d("ui.common.edit")}
                />
              )}
              {handleDuplicate && mode === "edit" && (
                <IconButton
                  {...commonIcons.duplicate}
                  onClick={eventWithoutPropagationDefault(handleDuplicate)}
                  skipFocus
                  label={d("ui.common.duplicate")}
                />
              )}
              {handleDelete && mode === "edit" && (
                <IconButton
                  {...commonIcons.delete}
                  onClick={eventWithoutPropagationDefault(handleDelete)}
                  skipFocus
                  label={d("ui.common.delete")}
                />
              )}
            </Inline>
            {showReorder && <ReorderIcon />}
          </Inline>
        )}
      </Inline>
      {isMultiLevel && !hideMultiLevel && (
        <Box
          style={{
            padding: "0 var(--spacing-150) var(--spacing-100)",
            marginTop: "calc(var(--spacing-050) * -1)"
          }}
        >
          <MultiLevelProperties
            showReorder={showReorder}
            question={question}
            path={path}
            d={d}
            isExpanded={isExpanded}
            onChangeExpanded={setIsExpanded}
            mode={mode}
          />
          {question.type === QuestionTypes.TABLE &&
            hasChartsInTableQuestion && (
              <ChartProperties
                question={question}
                path={path}
                d={d}
                isExpanded={isChartsExpanded}
                onChangeExpanded={setIsChartsExpanded}
                mode={mode}
              />
            )}
        </Box>
      )}
    </WhiteboardBlock>
  );
};

ConfigurationQuestionBlock.displayName = "ConfigurationQuestionBlock";
