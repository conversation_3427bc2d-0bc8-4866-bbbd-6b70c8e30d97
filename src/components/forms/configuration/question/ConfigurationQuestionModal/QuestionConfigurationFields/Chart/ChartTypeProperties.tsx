import React, { useCallback, useMemo } from "react";
import { useOutletContext } from "react-router-dom";

import { Doc, Prop } from "@automerge/automerge-repo";
import {
  ChartSubType,
  ChartType,
  Checkbox,
  MultiSelect,
  Select,
  SelectValue,
  Stack
} from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper";

import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { ChartSubTypes, Question } from "@src/types/Question.ts";
import {
  CartesianChartConfig,
  ChartConfig,
  ChartQuestionProperties,
  PieChartConfig,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { chartOnChange } from "./ChartHelper";

export const ChartTypeProperties = ({
  question,
  parentQuestion,
  path,
  d,
  isExpanded = true,
  onChangeExpanded,
  disabled
}: {
  question: Question<ChartQuestionProperties>;
  parentQuestion?: Question<TableQuestionProperties>;
  path: Prop[];
  d: Dictionary;
  isExpanded?: boolean;
  onChangeExpanded?: (expanded: boolean) => void;
  disabled?: boolean;
}) => {
  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const questionAccessor = useCallback(
    (accessor: string) => {
      return `${path.join(".")}.${accessor}`;
    },
    [path]
  );

  const chartType = question.properties?.chartConfig?.type;
  const subTypeOptions = useMemo(() => {
    const subTypes =
      chartType && ChartSubTypes[chartType] ? ChartSubTypes[chartType] : [];
    return (
      subTypes?.map(subType => ({
        label: d(
          `ui.configuration.forms.question.type.chart.type.${chartType}.subType.${subType}.label`
        ),
        value: subType,
        description: d(
          `ui.configuration.forms.question.type.chart.type.${chartType}.subType.${subType}.description`
        )
      })) ?? []
    );
  }, [chartType]);

  // Get all columns from the parent table question for now
  // Will need to be updated to limit to specific type of columns
  const getAllColumns = useMemo(() => {
    const columns = parentQuestion?.properties?.columns ?? [];
    return columns?.map(column => ({
      label: column.text,
      value: column.id
    }));
  }, [parentQuestion]);

  const renderSelectOrMulti = (
  key: string,
  isMulti: boolean,
  value: string | string[],
  onChange: (v: SelectValue | undefined) => void,
  labelKey: string
) => {
  const commonProps = {
    label: d(labelKey),
    name: questionAccessor(`properties.chartConfig.${key}`),
    disabled,
    options: getAllColumns,
    onlyTriggerChangeWhenBlur: true,
  };

  if (isMulti) {
    return (
      <MultiSelect
        width="100"
        {...commonProps}
        value={value as string[]}
        onChange={onChange}
      />
    );
  }
  return (
    <Select
      {...commonProps}
      value={(Array.isArray(value) ? value[0] : value) || ""}
      onChange={onChange}
    />
  );
};

const renderContent = useCallback(() => {
  const { chartConfig } = question.properties ?? {};
  if (!chartConfig) return null;

  const { type, swapColumns } = chartConfig;

  const isCartesian =
    type === ChartType.LINE || type === ChartType.BAR;
  const isPie = type === ChartType.PIE;

  if (isCartesian) {
    const xAxisIsMulti = !!swapColumns;
    const seriesIsMulti = !swapColumns;

    return (
      <>
        {renderSelectOrMulti(
          "xAxis",
          xAxisIsMulti,
          chartConfig.xAxis,
          v =>
            chartOnChange<
              CartesianChartConfig<ChartType.LINE> |
              CartesianChartConfig<ChartType.BAR>
            >()({
              field: "xAxis",
              path,
              docChange,
            })(
              Array.isArray(v)
                ? (v.filter((item): item is string => typeof item === "string") as string[])
                : [v].filter((item): item is string => typeof item === "string")
            ),
          "ui.configuration.forms.question.chart.xAxis"
        )}
        {renderSelectOrMulti(
          "series",
          seriesIsMulti,
          chartConfig.series,
          v =>
            chartOnChange<
              CartesianChartConfig<ChartType.LINE> |
              CartesianChartConfig<ChartType.BAR>
            >()({
              field: "series",
              path,
              docChange,
            })(Array.isArray(v) ? v : [v]),
          "ui.configuration.forms.question.chart.series"
        )}
      </>
    );
  }

  if (isPie) {
    const groupByIsMulti = !!swapColumns;
    const seriesIsMulti = !swapColumns;

    return (
      <>
        {renderSelectOrMulti(
          "groupBy",
          groupByIsMulti,
          chartConfig.groupBy,
          v =>
            chartOnChange<PieChartConfig>()({
              field: "groupBy",
              path,
              docChange,
            })(Array.isArray(v) ? v : [v]),
          "ui.configuration.forms.question.chart.groupBy"
        )}
        {renderSelectOrMulti(
          "series",
          seriesIsMulti,
          chartConfig.series,
          v =>
            chartOnChange<PieChartConfig>()({
              field: "series",
              path,
              docChange,
            })(Array.isArray(v) ? v[0] : v),
          "ui.configuration.forms.question.chart.series"
        )}
      </>
    );
  }

  return null;
}, [question, path, d, disabled, docChange]);

  // const renderContent = useCallback(() => {
  //   if (
  //     question.properties?.chartConfig.type === ChartType.LINE ||
  //     question.properties?.chartConfig.type === ChartType.BAR
  //   ) {
  //     return (
  //       <>
  //         {!question.properties?.chartConfig?.swapColumns ? (
  //           <>
  //             <Select
  //               label={d("ui.configuration.forms.question.chart.xAxis")}
  //               name={questionAccessor("properties.chartConfig.xAxis")}
  //               value={question.properties?.chartConfig?.xAxis[0] || ""}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<
  //                   | CartesianChartConfig<ChartType.LINE>
  //                   | CartesianChartConfig<ChartType.BAR>
  //                 >()({
  //                   field: "xAxis",
  //                   path,
  //                   docChange
  //                 })([v] as string[]);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //             <MultiSelect
  //               width="100"
  //               label={d("ui.configuration.forms.question.chart.series")}
  //               name={questionAccessor("properties.chartConfig.series")}
  //               value={question.properties?.chartConfig?.series}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<
  //                   | CartesianChartConfig<ChartType.LINE>
  //                   | CartesianChartConfig<ChartType.BAR>
  //                 >()({
  //                   field: "series",
  //                   path,
  //                   docChange
  //                 })(v as string[]);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //           </>
  //         ) : (
  //           <>
  //             <MultiSelect
  //               width="100"
  //               label={d("ui.configuration.forms.question.chart.xAxis")}
  //               name={questionAccessor("properties.chartConfig.xAxis")}
  //               value={question.properties?.chartConfig?.xAxis}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<
  //                   | CartesianChartConfig<ChartType.LINE>
  //                   | CartesianChartConfig<ChartType.BAR>
  //                 >()({
  //                   field: "xAxis",
  //                   path,
  //                   docChange
  //                 })(v as string[]);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //             <Select
  //               label={d("ui.configuration.forms.question.chart.series")}
  //               name={questionAccessor("properties.chartConfig.series")}
  //               value={question.properties?.chartConfig?.series[0] || ""}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<
  //                   | CartesianChartConfig<ChartType.LINE>
  //                   | CartesianChartConfig<ChartType.BAR>
  //                 >()({
  //                   field: "series",
  //                   path,
  //                   docChange
  //                 })([v] as string[]);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //           </>
  //         )}
  //       </>
  //     );
  //   } else {
  //     return (
  //       <>
  //         {!question.properties?.chartConfig?.swapColumns ? (
  //           <>
  //             <Select
  //               label={d("ui.configuration.forms.question.chart.groupBy")}
  //               name={questionAccessor("properties.chartConfig.groupBy")}
  //               value={question.properties?.chartConfig?.groupBy}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<PieChartConfig>()({
  //                   field: "groupBy",
  //                   path,
  //                   docChange
  //                 })([v] as string[]);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //             <MultiSelect
  //               width="100"
  //               label={d("ui.configuration.forms.question.chart.series")}
  //               name={questionAccessor("properties.chartConfig.series")}
  //               value={question.properties?.chartConfig?.series}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<PieChartConfig>()({
  //                   field: "series",
  //                   path,
  //                   docChange
  //                 })(v as string);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //           </>
  //         ) : (
  //           <>
  //             <MultiSelect
  //               width="100"
  //               label={d("ui.configuration.forms.question.chart.groupBy")}
  //               name={questionAccessor("properties.chartConfig.groupBy")}
  //               value={question.properties?.chartConfig?.groupBy}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<PieChartConfig>()({
  //                   field: "groupBy",
  //                   path,
  //                   docChange
  //                 })(v as string[]);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //             <Select
  //               label={d("ui.configuration.forms.question.chart.series")}
  //               name={questionAccessor("properties.chartConfig.series")}
  //               value={question.properties?.chartConfig?.series}
  //               disabled={disabled}
  //               options={getAllColumns}
  //               onChange={v => {
  //                 chartOnChange<PieChartConfig>()({
  //                   field: "series",
  //                   path,
  //                   docChange
  //                 })(v as string);
  //               }}
  //               onlyTriggerChangeWhenBlur
  //             />
  //           </>
  //         )}
  //       </>
  //     );
  //   }
  // }, [question, path, d, isExpanded, onChangeExpanded, disabled]);

  return (
    <Stack gap="100" className="multi-level-properties">
      <Select
        label={d("ui.configuration.forms.question.chart.subType")}
        name={questionAccessor("properties.chartConfig.subType")}
        value={question.properties?.chartConfig?.subType}
        disabled={disabled}
        options={subTypeOptions}
        onChange={v => {
          chartOnChange<ChartConfig>()({
            field: "subType",
            path,
            docChange
          })(v as ChartSubType);
        }}
        onlyTriggerChangeWhenBlur
      />
      {renderContent()}
      <Checkbox
        label={d("ui.configuration.forms.question.chart.swapColumns")}
        name={questionAccessor("properties.chartConfig.swapColumns")}
        isChecked={question.properties?.chartConfig?.swapColumns}
        disabled={disabled}
        onChange={v => {
          // chartOnChange<ChartConfig>()({
          //   field: "swapColumns",
          //   path,
          //   docChange
          // })(v);
          docChange(d => {
            const q = getByPath<Question<ChartQuestionProperties>>(d, path);
            if (!q?.properties) return;

            // Toggle swapColumns
            q.properties.chartConfig.swapColumns = v;

            // Swap or update xAxis and series values
            if (q.properties.chartConfig.type !== ChartType.PIE) {
              const prevXAxis = q.properties.chartConfig.xAxis;
              const prevSeries = q.properties.chartConfig.series;

              // If enabling swapColumns, move series to xAxis and xAxis to series
              q.properties.chartConfig.xAxis = prevSeries
                ? [...prevSeries]
                : [];
              q.properties.chartConfig.series = prevXAxis ? [...prevXAxis] : [];
            } else {
              const prevGroupBy = q.properties.chartConfig.groupBy;
              const prevSeries = q.properties.chartConfig.series;
              q.properties.chartConfig.groupBy = prevSeries ? [prevSeries] : [];
              q.properties.chartConfig.series =
                prevGroupBy && prevGroupBy.length > 0 ? prevGroupBy[0] : "";
            }
          });
        }}
      />
    </Stack>
  );
};

ChartTypeProperties.displayName = "ChartTypeProperties";
