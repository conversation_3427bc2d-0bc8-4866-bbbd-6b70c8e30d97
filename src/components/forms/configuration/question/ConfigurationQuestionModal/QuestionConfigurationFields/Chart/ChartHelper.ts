import { Prop } from "@automerge/automerge-repo";
import { ChartType } from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { Question } from "@src/types/Question.ts";
import {
  CartesianChartConfig,
  ChartConfig,
  ChartQuestionProperties,
  PieChartConfig
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

export type ChartConfigMap = {
  [ChartType.LINE]: CartesianChartConfig<ChartType.LINE>;
  [ChartType.BAR]: CartesianChartConfig<ChartType.BAR>;
  [ChartType.PIE]: PieChartConfig;
};

// export const chartOnChange =
//   <T extends ChartType, K extends keyof ChartConfigMap[T]>({
//     type,
//     field,
//     path,
//     docChange
//   }: {
//     type: T;
//     field: K;
//     path: Prop[];
//     docChange: (cb: (d: WorkspaceDocument) => void) => void;
//   }) =>
//   (value: ChartConfigMap[T][K]) => {
//     docChange((d: WorkspaceDocument): void => {
//       const q = getByPath<Question<ChartQuestionProperties>>(d, path);
//       if (!q?.properties) {
//         console.error("Question not found", path);
//         return;
//       }

//       const chartConfig = q.properties.chartConfig as ChartConfigMap[T];

//       if (
//         value === undefined ||
//         (Array.isArray(value) && value.length === 0) ||
//         (typeof value === "string" && value === "")
//       ) {
//         delete chartConfig[field];
//         return;
//       }

//       chartConfig[field] = value;
//     });
//   };

// export const chartOnChange =
//   <
//     C extends ChartConfig, // actual chart config type (Cartesian or Pie)
//     K extends keyof C // field key from that config
//   >({
//     field,
//     path,
//     docChange
//   }: {
//     field: K;
//     path: Prop[];
//     docChange: (cb: (d: WorkspaceDocument) => void) => void;
//   }) =>
//   (value: C[K]) => {
//     docChange((d: WorkspaceDocument): void => {
//       const q = getByPath<Question<ChartQuestionProperties>>(d, path);
//       if (!q?.properties) {
//         console.error("Question not found", path);
//         return;
//       }

//       const chartConfig = q.properties.chartConfig as C;

//       if (
//         value === undefined ||
//         (Array.isArray(value) && value.length === 0) ||
//         (typeof value === "string" && value === "")
//       ) {
//         delete chartConfig[field];
//         return;
//       }

//       chartConfig[field] = value;
//     });
//   };

export const chartOnChange = <C extends ChartConfig>() => {
  return <K extends keyof C>({
      field,
      path,
      docChange
    }: {
      field: K;
      path: Prop[];
      docChange: (cb: (d: WorkspaceDocument) => void) => void;
    }) =>
    (value: C[K]) => {
      docChange((d: WorkspaceDocument): void => {
        const q = getByPath<Question<ChartQuestionProperties>>(d, path);
        if (!q?.properties) {
          console.error("Question not found", path);
          return;
        }

        const chartConfig = q.properties.chartConfig as C;

        if (
          value === undefined ||
          (Array.isArray(value) && value.length === 0) ||
          (typeof value === "string" && value === "")
        ) {
          delete chartConfig[field];
          return;
        }

        chartConfig[field] = value;
      });
    };
};
