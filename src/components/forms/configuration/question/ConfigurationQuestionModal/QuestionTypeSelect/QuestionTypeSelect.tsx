import React, { useMemo } from "react";

import { Prop } from "@automerge/automerge-repo";
import { ChartType, Form, SelectOptions } from "@oneteam/onetheme";

import { getQuestionTypeOptions } from "@helpers/configurationFormHelper";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { QuestionTypes } from "@src/types/Question.ts";

export const QuestionTypeSelect = ({
  type,
  path,
  handleChange,
  disabled,
  options = getQuestionTypeOptions() as QuestionTypes[]
}: {
  type: `${QuestionTypes}` | `${ChartType}`;
  path: Prop[];
  handleChange: (type: QuestionTypes | ChartType) => void;
  disabled?: boolean;
  options?: QuestionTypes[] | ChartType[];
}) => {
  const d = useDictionary();
  const isChartType = useMemo(() => {
    return Object.values(ChartType).includes(type as ChartType);
  }, [type]);

  const questionTypeOptions: SelectOptions = useMemo(
    () =>
      options.map(type => {
        return {
          value: type,
          label: isChartType
            ? d(`ui.configuration.forms.question.type.chart.type.${type}.label`)
            : d(`ui.configuration.forms.question.type.${type}.label`),
          description: isChartType
            ? d(
                `ui.configuration.forms.question.type.chart.type.${type}.description`
              )
            : d(`ui.configuration.forms.question.type.${type}.description`)
        };
      }),
    [d, options]
  );

  const displayType = useMemo(() => {
    if (type === QuestionTypes.MULTISELECT) {
      return QuestionTypes.SELECT;
    }
    return type;
  }, [type]);

  return (
    <Form.PillSelect
      name={`${path.join(".")}.type`}
      value={displayType}
      options={questionTypeOptions}
      required
      leftElement={<QuestionType size="regular" type={type} iconOnly />}
      disabled={disabled}
      handleChange={value => {
        handleChange(value as QuestionTypes | ChartType);
      }}
      renderOptionLeftElement={({ option }) => (
        <QuestionType
          type={option?.value as QuestionTypes | ChartType}
          iconOnly
          size="regular"
        />
      )}
      fitOptionText
    />
  );
};
