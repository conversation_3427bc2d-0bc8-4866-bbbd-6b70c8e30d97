import React, { useMemo } from "react";

import { Prop } from "@automerge/automerge-repo";
import { ChartType, Form, SelectOptions } from "@oneteam/onetheme";
import { de } from "zod/v4/locales";

import { getQuestionTypeOptions } from "@helpers/configurationFormHelper";

import { QuestionType } from "@components/forms/QuestionType/QuestionType.tsx";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { ChartQuestionTypes, QuestionTypes } from "@src/types/Question.ts";

export const ChartTypeSelect = ({
  type,
  path,
  handleChange,
  disabled,
  options = ChartQuestionTypes
}: {
  type: `${ChartType}`;
  path: Prop[];
  handleChange: (type: ChartType) => void;
  disabled?: boolean;
  options?: ChartType[];
}) => {
  const d = useDictionary();

  const questionTypeOptions: SelectOptions = useMemo(
    () =>
      options.map(type => {
        return {
          value: type,
          // label: d(`ui.configuration.forms.question.type.${type}.label`),
          label: type
          // description: type
          // description: d(
          //   `ui.configuration.forms.question.type.${type}.description`
          // )
        };
      }),
    [d, options]
  );

  const displayType = useMemo(() => {
    // if (type === QuestionTypes.MULTISELECT) {
    //   return QuestionTypes.SELECT;
    // }
    return type;
  }, [type]);

  console.log("ChartTypeSelect", type, displayType);

  return (
    <Form.PillSelect
      name={`${path.join(".")}.type`}
      value={displayType}
      options={questionTypeOptions}
      required
      // leftElement={<QuestionType size="regular" type={type} iconOnly />}
      disabled={disabled}
      handleChange={value => {
        handleChange(value as ChartType);
      }}
      renderOptionLeftElement={({ option }) => (
        <QuestionType
          type={option?.value as ChartType}
          iconOnly
          size="regular"
        />
      )}
      fitOptionText
    />
  );
};
