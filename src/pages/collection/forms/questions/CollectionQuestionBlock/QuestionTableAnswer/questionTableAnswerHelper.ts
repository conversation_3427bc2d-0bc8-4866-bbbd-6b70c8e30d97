import { NumberType, formatDateForPicker } from "@oneteam/onetheme";

import { customNanoId } from "@helpers/customNanoIdHelper";

import { Question, QuestionTypes } from "@src/types/Question";
import {
  BooleanQuestionProperties,
  ListQuestionProperties,
  NumberQuestionProperties
} from "@src/types/QuestionProperties";
import { FormAnswer, ListAnswer } from "@src/types/collection/CollectionForm";

type GetCellValueToSave = (params: {
  question: Question;
  value?: FormAnswer["value"];
  prevAnswer?: FormAnswer["value"];
}) => FormAnswer["value"];

const fallbackCellValueToSave: {
  [key in QuestionTypes]: () => FormAnswer["value"];
} = {
  [QuestionTypes.TEXT]: () => "",
  [QuestionTypes.NUMBER]: () => "",
  [QuestionTypes.BOOLEAN]: () => undefined,
  [QuestionTypes.DATE]: () => "",
  [QuestionTypes.SELECT]: () => undefined,
  [QuestionTypes.MULTISELECT]: () => [],
  [QuestionTypes.TABLE]: () => ({
    entities: {},
    order: []
  }),
  [QuestionTypes.FILES]: () => [],
  [QuestionTypes.LIST]: () => ({
    entities: {},
    order: []
  }),
  [QuestionTypes.SCHEMA]: () => ({}),
  [QuestionTypes.JSON]: () => ({}),
  [QuestionTypes.CHART]: () => []
};

// These are the values that will be displayed in the table cell and will be searchable
export const getCellValueToSave: GetCellValueToSave = ({ question, value }) => {
  const getCellValueToSaveFunction =
    getCellValueToSaveFunctionByType[question.type];
  if (getCellValueToSaveFunction) {
    return getCellValueToSaveFunction({ question, value });
  }
  return value ?? fallbackCellValueToSave[question.type]();
};

const getNumberCellValueToSave: GetCellValueToSave = ({ question, value }) => {
  if (value === undefined || value === null || value === "") {
    return fallbackCellValueToSave[question.type]();
  }
  let stringValue = value?.toString().trim().replaceAll(",", "");
  const type = (question.properties as NumberQuestionProperties)?.type;
  const decimalPlaces = (question.properties as NumberQuestionProperties)
    ?.decimalPlaces;

  if (type === NumberType.PERCENTAGE) {
    if (stringValue.endsWith("%")) {
      // Remove the percentage sign and divide by 100
      stringValue = stringValue.slice(0, -1).trim();
      const resolvedValue = parseFloat(stringValue) / 100;
      if (!isNaN(resolvedValue)) {
        return getNumberAsStringWithDecimals(resolvedValue, decimalPlaces);
      }
    } else {
      const resolvedValue = parseFloat(stringValue);
      if (!isNaN(resolvedValue)) {
        return getNumberAsStringWithDecimals(resolvedValue, decimalPlaces);
      }
    }
  }

  const resolvedValue = parseFloat(stringValue);

  if (!isNaN(resolvedValue)) {
    return getNumberAsStringWithDecimals(resolvedValue, decimalPlaces);
  }

  return fallbackCellValueToSave[question.type]();
};

const getBooleanCellValueToSave: GetCellValueToSave = ({ question, value }) => {
  if (value === undefined || value === null || value === "") {
    return fallbackCellValueToSave[question.type]();
  }
  if (typeof value === "boolean") {
    return value;
  }

  const lowercaseStringValue = value?.toString().toLowerCase();
  const yesLabel = (
    (question.properties as BooleanQuestionProperties).trueText || "Yes"
  ).toLowerCase();
  const noLabel = (
    (question.properties as BooleanQuestionProperties).falseText || "No"
  ).toLowerCase();

  if (
    lowercaseStringValue === yesLabel ||
    lowercaseStringValue === "true" ||
    lowercaseStringValue === "1"
  ) {
    return true;
  } else if (
    lowercaseStringValue === noLabel ||
    lowercaseStringValue === "false" ||
    lowercaseStringValue === "0"
  ) {
    return false;
  }
  return fallbackCellValueToSave[question.type]();
};

const getMultiSelectCellValueToSave: GetCellValueToSave = ({
  question,
  value
}) => {
  if (value === undefined || value === null || value === "") {
    return fallbackCellValueToSave[question.type]();
  }
  if (Array.isArray(value)) {
    return value;
  } else if (value && typeof value === "string") {
    const values = value
      .split(",")
      .map(v => v.trim())
      .filter(Boolean);
    return values.length > 0
      ? values
      : fallbackCellValueToSave[question.type]();
  }
  return fallbackCellValueToSave[question.type]();
};

const getDateCellValueToSave: GetCellValueToSave = ({ question, value }) => {
  if (value === undefined || value === null || value === "") {
    return fallbackCellValueToSave[question.type]();
  }
  const date = new Date(value.toString().trim());
  if (isNaN(date.getTime())) {
    return fallbackCellValueToSave[question.type]();
  }
  return formatDateForPicker(date);
};

const getListCellValueToSave: GetCellValueToSave = ({
  question,
  value,
  prevAnswer
}) => {
  if (value === undefined || value === null || value === "") {
    return fallbackCellValueToSave[question.type]();
  }
  if (value && typeof value === "string") {
    const listOf = (question.properties as ListQuestionProperties)
      ?.items[0] as Question;
    if (!listOf) {
      return fallbackCellValueToSave[question.type]();
    }
    const prevItemIdByValue =
      (prevAnswer as ListAnswer)?.order?.reduce(
        (acc, id) => {
          const item = (prevAnswer as ListAnswer).entities[id];
          const itemValue = item.item[listOf.id].value?.toString() ?? "";
          acc[itemValue] = item.id;
          return acc;
        },
        {} as Record<string, string>
      ) || {};
    const values = value
      .split(",")
      .map(v =>
        getCellValueToSave({
          question: listOf,
          value: v.trim()
        })
      )
      .filter(Boolean);

    const answer: ListAnswer = {
      entities: {},
      order: [] as string[]
    };

    values.forEach(value => {
      const itemValue = value?.toString() ?? "";
      if (!itemValue) {
        return; // Skip empty values
      }
      let prevId = prevItemIdByValue[itemValue];
      if (prevId) {
        delete prevItemIdByValue[itemValue];
      } else {
        prevId = customNanoId();
      }
      answer.entities[prevId] = {
        id: prevId,
        item: {
          [listOf.id]: {
            questionId: listOf.id,
            value,
            type: listOf.type
          }
        }
      };
      answer.order.push(prevId);
    });
    return answer;
  }
  return value ?? fallbackCellValueToSave[question.type]();
};

const getCellValueToSaveFunctionByType: {
  [key: string]: GetCellValueToSave;
} = {
  [QuestionTypes.NUMBER]: getNumberCellValueToSave,
  [QuestionTypes.BOOLEAN]: getBooleanCellValueToSave,
  [QuestionTypes.DATE]: getDateCellValueToSave,
  [QuestionTypes.MULTISELECT]: getMultiSelectCellValueToSave,
  [QuestionTypes.LIST]: getListCellValueToSave
};

type GetCellValueToDisplay = (params: {
  question: Question;
  value?: FormAnswer["value"];
}) => FormAnswer["value"];

// These are the values that will be displayed in the table cell and will be searchable
export const getCellValueToDisplay: GetCellValueToDisplay = ({
  question,
  value
}) => {
  const getCellValueToDisplayFunction =
    getCellValueToDisplayFunctionByType[question.type];
  if (getCellValueToDisplayFunction) {
    return getCellValueToDisplayFunction({ question, value });
  }
  return getCellValueToSave({ question, value });
};

const getNumberCellValueToDisplay: GetCellValueToDisplay = ({
  question,
  value
}) => {
  if (value === "" || value === null || value === undefined) {
    return fallbackCellValueToSave[question.type]();
  }
  const decimalPlaces = (question.properties as NumberQuestionProperties)
    ?.decimalPlaces;
  if (
    (question.properties as NumberQuestionProperties)?.type ===
    NumberType.PERCENTAGE
  ) {
    if (typeof value === "number") {
      return `${getNumberAsStringWithDecimals(value * 100, decimalPlaces)}%`;
    } else if (typeof value === "string" && value.endsWith("%")) {
      return value;
    }
    return `${getNumberAsStringWithDecimals(parseFloat(value as string) * 100, decimalPlaces, -2)}%`;
  }

  const valueAsNumber = parseFloat(value as string);
  if (!isNaN(valueAsNumber)) {
    return getNumberAsStringWithDecimals(valueAsNumber, decimalPlaces);
  }
  return fallbackCellValueToSave[question.type]();
};

const getBooleanCellValueToDisplay: GetCellValueToDisplay = ({
  question,
  value
}) => {
  const yesLabel =
    (question.properties as BooleanQuestionProperties).trueText || "Yes";
  const noLabel =
    (question.properties as BooleanQuestionProperties).falseText || "No";

  if (value === true) {
    return yesLabel;
  } else if (value === false) {
    return noLabel;
  }
  return fallbackCellValueToSave[question.type]();
};

const getDateCellValueToDisplay: GetCellValueToDisplay = ({
  question,
  value
}) => {
  if (value === "" || value === null || value === undefined) {
    return fallbackCellValueToSave[question.type]();
  }
  if (typeof value === "string") {
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return fallbackCellValueToSave[question.type]();
    }
    return formatDateForPicker(date);
  }
  return fallbackCellValueToSave[question.type]();
};

const getListCellValueToDisplay: GetCellValueToDisplay = ({
  question,
  value
}) => {
  if (value && (value as ListAnswer)?.order) {
    const listOf = (question.properties as ListQuestionProperties)
      .items[0] as Question;
    return (value as ListAnswer).order
      ?.map((id: string) => {
        const listItemValue = Object.values(
          (value as ListAnswer).entities[id].item
        )?.[0].value as FormAnswer["value"];
        return (
          getCellValueToDisplay({
            question: listOf,
            value: listItemValue
          }) || ""
        );
      })
      .filter(Boolean)
      .join(",");
  }
  return "";
};

const getCellValueToDisplayFunctionByType: {
  [key: string]: GetCellValueToDisplay;
} = {
  [QuestionTypes.NUMBER]: getNumberCellValueToDisplay,
  [QuestionTypes.BOOLEAN]: getBooleanCellValueToDisplay,
  [QuestionTypes.DATE]: getDateCellValueToDisplay,
  [QuestionTypes.LIST]: getListCellValueToDisplay
};

const getNumberAsStringWithDecimals = (
  num: number,
  decimalPlaces?: number | null,
  offsetDecimals: number = 0 // For percentage handling, default is 0
): string => {
  if (num === null || num === undefined) {
    return "";
  }
  if (decimalPlaces === undefined || decimalPlaces === null) {
    return num.toString();
  }
  return num.toFixed(decimalPlaces + offsetDecimals);
};
